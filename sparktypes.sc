
import io.github.pashashiz.spark_encoders.TypedEncoder
import scala.util.chaining.*


sealed trait Person

case class Adult(name: String, age: Int, birthday: Option[Int]) extends Person
case class Child(name: String, age: Int, birthday: Option[Int], guardian: String) extends Person
case class Senior(name: String, age: Int, birthday: Option[Int], pensionId: String) extends Person

object Person {
    def apply(name: String, age: Int, birthday: Option[Int]): Person = {
        age match {
            case x if x < 18  => Child(name, age, birthday, "Unknown")
            case x if x >= 65 => Senior(name, age, birthday, "N/A")
            case _            => Adult(name, age, birthday)
        }
    }
}

val enc = TypedEncoder[Option[Int]].encoder

enc.schema

enc.schema.sql.pipe(println)

enc.schema.printTreeString()

enc.schema.prettyJson

val pEnc = TypedEncoder[Person].encoder

pEnc
    .schema
    .printTreeString()

pEnc
    .deserializer
    .dataType

pEnc
    .deserializer
    .numberedTreeString

pEnc
    .deserializer
    .simpleStringWithNodeId()